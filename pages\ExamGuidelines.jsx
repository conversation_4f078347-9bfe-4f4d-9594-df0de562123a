import React, { useState, useEffect } from 'react';

const ExamGuidelines = () => {
  const [isChecked, setIsChecked] = useState(false);
  const [countdown, setCountdown] = useState(5);
  const [isProceedEnabled, setIsProceedEnabled] = useState(false);

  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prevCountdown) => prevCountdown - 1);
      }, 1000);
    } else if (countdown === 0) {
      setIsProceedEnabled(true);
    }
    return () => clearInterval(timer);
  }, [countdown]);

  const handleCheckboxChange = (event) => {
    setIsChecked(event.target.checked);
    if (!event.target.checked) {
      setIsProceedEnabled(false);
    }
  };

  return (
    <div className="bg-white flex items-center justify-center p-4 font-sans">
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-[#C4D5FF4D] p-16 shadow-lg z-10 h-[85vh] w-9/12 rounded-3xl flex flex-col items-center justify-between " style={{ borderColor: "#B2BCEE" }}>
        <h1 className="text-4xl font-bold text-center text-[#3A1078] mb-2">
          Exam Guidelines
        </h1>

        <div className="space-y-10 text-[#3A1078] text-base leading-relaxed w-full">
         
          <p>
            Eos libero fugit orem autt nem s libero fugit orem autt nemo ss libero fugit orem autt nemo ss libero fugit orem autt nemo ss libero fugit orem autt nemo ss libero fugit orem autt nemo ss libero fugit orem autt nemo ss libero fugit orem autt nemo ss libero fugit orem autt nemo ss libero fugit orem autt nemo ss s libero fugit orem autt nemo ss libero fugit orem autt nemo ss libero fugit orem autt nemo ss libero fugit orem autt nemo ss libero fugit orem autt nemo ss libero fugit orem autt nemo ss libero fugit orem autt nemo ss libero fugit orem autt nemo ss libero fugit orem autt nemo ss libero fugit orem autt nemo ss libero fugit orem autt nemo ss libero fugit orem autt nemo s libero fugit orem autt nemo ss libero fugit orem autt nemo so sunt!
          </p>
        </div>

        <div className="w-full flex flex-col items-center space-y-4">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="readInstructions"
              checked={isChecked}
              onChange={handleCheckboxChange}
              className="h-3 w-3 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
            />
            <label
              htmlFor="readInstructions"
              className="ml-2 text-base text-gray-700 select-none"
            >
              <img
                src="/instructions.svg"
                alt="I Have Read All The Terms And Conditions And I Agree"
                className="ml-2 h-4 w-auto"
              />
            </label>
          </div>

          <button
            disabled={!isProceedEnabled || !isChecked}
            className={`px-10 py-3 text-lg font-semibold text-white rounded-lg shadow-md transition-colors duration-300 ease-in-out
              ${isProceedEnabled && isChecked
                ? 'bg-indigo-600 hover:bg-indigo-700 focus:ring-4 focus:ring-indigo-300 cursor-pointer'
                : 'bg-gray-400 cursor-not-allowed'
              }`}
          >
            Proceed ({countdown})
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExamGuidelines;