import React from 'react';
import { FiCheckCircle, FiChevronDown, FiShare2, FiRefreshCw } from 'react-icons/fi';

const ExamReport = () => {
  const questions = [
    {
      qNo: 1,
      question: 'What is the SI unit of force?',
      correctAnswer: 'Newton',
      yourAnswer: 'Newton',
      score: 4,
    },
    {
      qNo: 2,
      question: 'What is the SI unit of force?',
      correctAnswer: 'Newton',
      yourAnswer: 'Newton',
      score: 7,
    },
    {
      qNo: 3,
      question: 'What is the SI unit of force?',
      correctAnswer: 'Newton',
      yourAnswer: 'Laden',
      score: 2,
    },
  ];

  // Calculate accuracy (assuming total questions is 20 and correct answers is 16 as shown in stats)
  const accuracy = 80; // This would normally be calculated dynamically

  const status =
    accuracy >= 90 ? 'A+' :
    accuracy >= 80 ? ' A' :
    accuracy >= 70 ? 'B+' : 
    accuracy >= 60 ? 'B' : 
    accuracy >= 50 ? ' C' : 
    accuracy >= 40 ? 'D' : 
    'Failed';

  const statusColor = 
    status === ' A+' ? 'text-green-600' :
    status === ' A'  ? 'text-green-500' :
    status === ' B+' ? 'text-blue-500'  :
    status === ' B'  ? 'text-blue-400'  :
    status === ' C'  ? 'text-yellow-500' :
    status === ' D'  ? 'text-orange-500' :
    'text-red-600'; // Failed

  const getScoreColor = (score) => {
    if (score >= 6) return 'text-green-600';
    if (score >= 3) return 'text-yellow-500';
    return 'text-red-600';
  };

  const handleShareScore = () => {
    window.print(); // This will trigger the browser's print dialog
  };

  return (
    <div className="min-h-screen bg-slate-100 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl bg-white shadow-lg rounded-lg">
        {/* Header - Gradient Part */}
        <div
          className="text-white p-4 rounded-t-lg"
          style={{ background: 'linear-gradient(90deg, #A6C8FF 0%, #CABDFF 100%)' }}
        >
          <h1 className="text-lg font-semibold">Exam Report</h1>
        </div>

        <div className="p-6 md:p-8 space-y-8">
          {/* Topic Name, Student Info & Stats */}
          <div className="bg-white p-6 rounded-lg shadow-md text-black">
            <h2 className="text-2xl font-bold text-blue-800 mb-1">Topic Name Fetched from Backend</h2>
            
            <div className="flex flex-col md:flex-row justify-between items-start mt-3">
              {/* Left Side: Student Name, Date, Duration */}
              <div className="mb-4 md:mb-0">
                <h3 className="text-xl font-semibold text-gray-700">Arindam Kanrar</h3>
                <p className="text-sm text-gray-500 mt-8">
                  Date: 15 Nov 2023
                </p>
                <p className="text-sm text-gray-500">
                  Duration: 30 minutes
                </p>
              </div>

              {/* Right Side: Stats Grid */}
              <div className="grid grid-cols-2 gap-3 w-full md:w-auto md:min-w-[280px] md:max-w-xs text-left">
                <div className="bg-gray-50 p-3 rounded-lg shadow-sm">
                  <p className="text-xs text-gray-500">Total Questions</p>
                  <p className="text-lg font-bold text-gray-800">20</p>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg shadow-sm">
                  <p className="text-xs text-gray-500">Correct Answers</p>
                  <p className="text-lg font-bold text-green-600">16</p>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg shadow-sm">
                  <p className="text-xs text-gray-500">Accuracy</p>
                  <p className="text-lg font-bold text-gray-800">80%</p>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg shadow-sm">
                  <p className="text-xs text-gray-500">Status</p>
                  <div className="flex items-center">
                    <p className={`text-lg font-semibold ${statusColor}`}>{status}</p>
                  
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Question Breakdown */}
          <div className="bg-white p-6 rounded-lg shadow-md text-black">
            <div className="border-b border-gray-200 pb-4 flex flex-col sm:flex-row justify-between items-center mb-4">
              <h3 className="text-xl font-semibold text-gray-800 mb-3 sm:mb-0">Question Breakdown</h3>
              <div className="flex items-center space-x-2">
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full text-sm text-left text-gray-700 border border-gray-300/80">
                <thead className="text-xs text-gray-700 uppercase bg-gray-100 border-b border-gray-300/80">
                  <tr className="bg-white border-b border-gray-300/80 hover:bg-gray-50">
                    <th scope="col" className="px-4 py-3">Q.No</th>
                    <th scope="col" className="px-6 py-3">Question</th>
                    <th scope="col" className="px-6 py-3">Correct Answer</th>
                    <th scope="col" className="px-6 py-3">Your Answer</th>
                    <th scope="col" className="px-4 py-3 text-center">Score(?/10)</th>
                  </tr>
                </thead>
                <tbody>
                  {questions.map((q) => (
                    <tr key={q.qNo} className="bg-white border-b hover:bg-gray-50">
                      <td className="px-4 py-4 font-medium text-gray-900">{q.qNo}</td>
                      <td className="px-6 py-4">{q.question}</td>
                      <td className="px-6 py-4 text-black-600 font-medium">{q.correctAnswer}</td>
                      <td className={`px-6 py-4 font-medium ${q.yourAnswer === q.correctAnswer ? 'text-black-600' : 'text-black-600'}`}>
                        {q.yourAnswer}
                      </td>
                      <td className={`px-4 py-4 text-center font-bold ${getScoreColor(q.score)}`}>
                        {q.score}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Personalized Feedback */}
          <div className="bg-white p-6 rounded-lg shadow-md text-black">
            <div className="flex items-center mb-4">
              <div className="bg-white p-2 rounded-full mr-3">
                <img
                  src="/Margin.svg" 
                  alt="AI Mentor Icon"
                  className="h-10 w-10 text-purple-700" 
                />
              </div>
              <h4 className="text-xl font-semibold">Personalized Feedback from AI Mentor</h4>
            </div>
            <p className="text-sm mb-3">
              Strengths: Your performance in Mathematics and Computer Science is exceptional, showing strong analytical and problem-solving skills. You've demonstrated consistent improvement throughout the term.
            </p>
            <p className="text-sm mb-4">
              Areas to Improve: English scores show room for improvement, particularly in essay writing. Focus on expanding vocabulary and structuring arguments more effectively.
            </p>
            <div>
              <h5 className="font-semibold mb-2">Recommendations:</h5>
              <ul className="list-disc list-inside text-sm space-y-1">
                <li>Use our AI Writing Assistant for English practice</li>
                <li>Join the weekly problem-solving club for Math enrichment</li>
                <li>Allocate 30 minutes daily for vocabulary building</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Footer Actions */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg flex flex-col sm:flex-row justify-between items-center">
          <div className="text-sm text-gray-600 mb-4 sm:mb-0">
            Attempts: <span className="font-semibold">3</span> | Best Score: <span className="font-semibold text-gray-600">85%</span>
          </div>
          <div className="flex items-center space-x-3">
           
            <button 
                         className="text-sm text-[#7C3AED] bg-white hover:bg-purple-400 border border-[#7C3AED] font-medium py-2 px-4 rounded-md flex items-center"
                         onClick={handleShareScore}
                       >
                         <FiShare2 className="mr-2 h-4 w-4" /> Share Score
                       </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExamReport;