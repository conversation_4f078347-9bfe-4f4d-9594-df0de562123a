import React, { useState, useRef } from 'react';
import { UploadCloud, Plus, Minus, ChevronDown, Clock } from 'lucide-react';

const CreateExamPage = () => {
  const [examType, setExamType] = useState('mcq'); // 'mcq' or 'handwritten'
  const [activeTab, setActiveTab] = useState('upload'); // 'upload' or 'topic'

  // Time related states
  const [displayTime, setDisplayTime] = useState(''); // For the HH : MM : SS input
  const [timeOption, setTimeOption] = useState('preset'); // 'preset', '15', '30', 'custom', etc. (from dropdown)
  const [customTimeInput, setCustomTimeInput] = useState(''); // For the input shown when dropdown's "Custom" is selected

  const [questionsOption, setQuestionsOption] = useState('preset'); // 'preset' or 'custom' (from dropdown)
  const [numQuestions, setNumQuestions] = useState(10);
  const [customQuestions, setCustomQuestions] = useState('');

  const [difficulty, setDifficulty] = useState('');
  const fileInputRef = useRef(null);

  const handleExamTypeChange = (type) => {
    setExamType(type);
  };

  const handleIncrementQuestions = () => {
    setNumQuestions(prev => prev + 1);
  };

  const handleDecrementQuestions = () => {
    setNumQuestions(prev => (prev > 1 ? prev - 1 : 1));
  };

  const handleFileBrowse = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      console.log("Selected file:", file.name);
    
    }
  };

  const handleTimeDropdownChange = (e) => {
    const selectedValue = e.target.value;
    setTimeOption(selectedValue);

    if (selectedValue === 'custom') {
    
      setCustomTimeInput('');
    } else if (selectedValue !== 'preset' && selectedValue !== "") { // "" is value of "Select Custom" hidden option
      const minutes = parseInt(selectedValue);
      if (!isNaN(minutes)) {
        const h = Math.floor(minutes / 60);
        const m = minutes % 60;
        setDisplayTime(`${String(h).padStart(2, '0')} : ${String(m).padStart(2, '0')} : 00`);
      } else {
        setDisplayTime(''); // Reset if not a valid minute value
      }
      setCustomTimeInput(''); 
    } else { 
      if(selectedValue === "preset") setDisplayTime('');
    }
  };

  const handleDisplayTimeChange = (e) => {
    setDisplayTime(e.target.value);
 
    if (timeOption === 'custom') { // If it was on 'custom', switch it back
        setTimeOption('preset'); // Or a unique value that signifies manual input
    }
  };


  return (
    <div className="min-h-screen bg-gray-100 flex flex-col items-center py-10 px-4">
      <div className="w-full max-w-4xl">
        <div className="flex items-center mb-8">
          <button className="text-gray-600 hover:text-gray-800">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-6 h-6">
              <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
            </svg>
          </button>
          <h1 className="text-3xl font-bold text-blue-800 ml-4">Create Exam</h1>
        </div>
        <p className="text-gray-600 mb-8">Enter Topic or Upload PDF and Select your preference</p>

        <div
          className="rounded-lg p-1 flex mb-8 w-full" 
          style={{ background: "linear-gradient(90deg, #2164D7 0%, #AA3AEB 100%)" }}>
          <button
            className={`flex-1 py-2 px-4 text-center rounded-md font-semibold transition-colors duration-300 ease-in-out
              ${examType === 'mcq' ? 'bg-white text-purple-700 shadow-md' : 'bg-transparent text-white hover:bg-purple-500 hover:bg-opacity-30'}`}
            onClick={() => handleExamTypeChange('mcq')}
          >
            MCQ Exam
          </button>
          <button
            className={`flex-1 py-2 px-4 text-center rounded-md font-semibold transition-colors duration-300 ease-in-out
              ${examType === 'handwritten' ? 'bg-white text-purple-700 shadow-md' : 'bg-transparent text-white hover:bg-purple-500 hover:bg-opacity-30'}`}
            onClick={() => handleExamTypeChange('handwritten')}
          >
            Handwritten Exam
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Left Side: Upload/Enter Topic */}
          <div className="bg-white p-8 rounded-lg shadow-lg">
            <div className="flex border-b mb-6">
              <button
                className={`py-3 px-4 font-semibold ${activeTab === 'upload' ? 'text-purple-700 border-b-2 border-purple-700' : 'text-gray-500 hover:text-purple-700 hover:border-b-2 hover:border-purple-700'}`}
                onClick={() => setActiveTab('upload')}
              >
                Upload PDF
              </button>
              <button
                className={`py-3 px-4 font-semibold ${activeTab === 'topic' ? 'text-purple-700 border-b-2 border-purple-700' : 'text-gray-500 hover:text-purple-700 hover:border-b-2 hover:border-purple-700'}`}
                onClick={() => setActiveTab('topic')}
              >
                Enter Topic
              </button>
            </div>

            {activeTab === 'upload' && (
              <div className="border-2 border-dashed border-blue-300 rounded-lg p-10 flex flex-col items-center justify-center text-center h-64">
                <UploadCloud className="w-16 h-16 text-gray-400 mb-4" /> {/* Adjusted icon size */}
                <p className="text-gray-600 mb-2">Drag & drop your syllabus file here</p>
                <p className="text-gray-500 text-sm mb-4">or</p>
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  accept=".pdf"
                  className="hidden"
                />
                <button
                  onClick={handleFileBrowse}
                  className="text-white font-semibold py-3 px-6 rounded-lg hover:opacity-90 transition-colors"
                  style={{ background: "linear-gradient(178.36deg, #4387FF -31.43%, #604AD8 98.61%)" }}
                >
                  Browse Files
                </button>
                <p className="text-xs text-gray-400 mt-2">Supported Only : PDF</p>
              </div>
            )}

            {activeTab === 'topic' && (
              <div className="border-2 border-dashed border-blue-300 rounded-lg p-6 flex flex-col items-center justify-center h-64">
                <textarea
                  placeholder="Enter your topic details here..."
                  className="w-full h-full p-4 text-gray-700 bg-transparent focus:outline-none resize-none placeholder-gray-500 text-sm"
                />
              </div>
            )}
          </div>

          {/* Right Side: Settings */}
          <div className="space-y-8">
            {/* Choose Time */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Choose Time</label>
              <div className="flex items-center space-x-2">
                <div className="relative flex-1">
                  <input
                    type="text"
                    placeholder="HH : MM : SS"
                    value={displayTime}
                    onChange={handleDisplayTimeChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500 disabled:bg-gray-50"
                    disabled={timeOption === 'custom'} // Disabled if dropdown's "Custom" is selected
                  />
                  <Clock className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                </div>
                <span className="text-gray-500">OR</span>
                <div className="relative">
                  <select
                    className="appearance-none w-full bg-white border border-gray-300 text-gray-700 py-3 px-4 pr-8 rounded-lg leading-tight focus:outline-none focus:bg-white focus:border-purple-500"
                    value={timeOption} // Controlled by timeOption state
                    onChange={handleTimeDropdownChange}
                  >
                    <option value="preset" disabled hidden>Select Custom</option>
                    <option value="15">15 Minutes</option>
                    <option value="30">30 Minutes</option>
                    <option value="45">45 Minutes</option>
                    <option value="60">1 Hour</option>
                    <option value="custom">Custom</option>
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                    <ChevronDown className="w-4 h-4" />
                  </div>
                </div>
              </div>
              {timeOption === 'custom' && ( // Show this input only if dropdown's "Custom" is selected
                <input
                  type="text"
                  placeholder="Enter custom time (e.g., 20m, 1h 30m)"
                  className="mt-2 w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
                  value={customTimeInput}
                  onChange={(e) => setCustomTimeInput(e.target.value)}
                />
              )}
            </div>

            {/* Select No of Questions */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Select No of Questions</label>
              <div className="flex items-center space-x-2">
                <div className="flex items-center border border-gray-300 rounded-lg">
                  <button
                    onClick={handleDecrementQuestions}
                    className="p-3 text-purple-600 hover:bg-gray-100 rounded-l-lg disabled:opacity-50 disabled:bg-gray-50"
                    disabled={questionsOption === 'custom'}
                  >
                    <Minus className="w-5 h-5" />
                  </button>
                  <input
                    type="number"
                    value={numQuestions}
                    readOnly // To be controlled by +/- buttons unless custom is not selected
                    onChange={(e) => { // Allow typing if not in 'custom' mode from dropdown
                        if (questionsOption !== 'custom') {
                            setNumQuestions(parseInt(e.target.value) || 1)
                        }
                    }}
                    className="w-16 text-center border-l border-r border-gray-300 py-3 focus:outline-none disabled:bg-gray-50"
                    disabled={questionsOption === 'custom'}
                  />
                  <button
                    onClick={handleIncrementQuestions}
                    className="p-3 text-purple-600 hover:bg-gray-100 rounded-r-lg disabled:opacity-50 disabled:bg-gray-50"
                    disabled={questionsOption === 'custom'}
                  >
                    <Plus className="w-5 h-5" />
                  </button>
                </div>
                <span className="text-gray-500">OR</span>
                 <div className="relative">
                  <select
                    className="appearance-none w-full bg-white border border-gray-300 text-gray-700 py-3 px-4 pr-8 rounded-lg leading-tight focus:outline-none focus:bg-white focus:border-purple-500"
                    value={questionsOption}
                    onChange={(e) => {
                        const selectedVal = e.target.value;
                        setQuestionsOption(selectedVal);
                        if (selectedVal !== 'custom') {
                           // If a preset number is chosen, update numQuestions
                           if (!isNaN(parseInt(selectedVal))) {
                               setNumQuestions(parseInt(selectedVal));
                           }
                           setCustomQuestions('');
                        } else {
                           // Optionally, reset numQuestions or leave it
                        }
                    }}
                  >
                    <option value="preset" disabled hidden>Select Custom</option>
                    <option value="5">5 Questions</option>
                    <option value="10">10 Questions</option>
                    <option value="15">15 Questions</option>
                    <option value="20">20 Questions</option>
                    <option value="custom">Custom</option>
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                    <ChevronDown className="w-4 h-4" />
                  </div>
                </div>
              </div>
               {questionsOption === 'custom' && (
                <input
                  type="number"
                  placeholder="Enter custom number"
                  className="mt-2 w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
                  value={customQuestions}
                  onChange={(e) => setCustomQuestions(e.target.value)}
                />
              )}
            </div>

            {/* Select Difficulty Level */}
            <div>
              <label htmlFor="difficulty" className="block text-sm font-medium text-gray-700 mb-2">Select Difficulty Level</label>
              <div className="relative">
                <select
                  id="difficulty"
                  name="difficulty"
                  className="appearance-none w-full bg-white border border-gray-300 text-gray-700 py-3 px-4 pr-8 rounded-lg leading-tight focus:outline-none focus:bg-white focus:border-purple-500"
                  value={difficulty}
                  onChange={(e) => setDifficulty(e.target.value)}
                >
                  <option value="" disabled hidden>Select Custom</option> {/* Changed from "Select Custom" to "Select Difficulty" or similar if preferred */}
                  <option value="easy">Easy</option>
                  <option value="medium">Medium</option>
                  <option value="hard">Hard</option>
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                  <ChevronDown className="w-4 h-4" />
                </div>
              </div>
            </div>

            <button className="w-full bg-purple-600 text-white font-semibold py-4 px-6 rounded-lg hover:bg-purple-700 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50">
              Start Exam
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateExamPage;