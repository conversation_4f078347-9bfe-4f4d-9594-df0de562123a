import React, { useState, useRef, useEffect } from 'react';
import { MdOutlineDownloadForOffline } from "react-icons/md";
import { HiRefresh } from "react-icons/hi";
import { IoCloseCircleOutline } from "react-icons/io5";
import { FaMicrophone } from "react-icons/fa";
import { IoIosVideocam } from "react-icons/io";
import { IoSend } from "react-icons/io5";
import SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition';
import PreferenceModal from './modals/PreferenceModal';
import { createAvatarSynthesizer, createWebRTCConnection } from "./Utility";
import { avatarAppConfig } from "./config";
import axios from 'axios';
import * as SpeechSDK from "microsoft-cognitiveservices-speech-sdk";

/* ────────────────────────────────────────────────────────────────────────── */
/*  🔈 1.  PUT YOUR SPEECH KEY & REGION IN ENV VARIABLES (recommended)       */
/* ------------------------------------------------------------------------ */
const SPEECH_KEY = import.meta.env.VITE_AZURE_SPEECH_KEY;      // or a hard-coded test key
const SPEECH_REGION = import.meta.env.VITE_AZURE_SPEECH_REGION; // e.g. "eastus"


const Viva = () => {
    // Chat state
    const [avatarSynthesizer, setAvatarSynthesizer] = useState(null);
    const [interviewStarted, setInterviewStarted] = useState(false);
    const [language, setLanguage] = useState("hindi");
    const [topic, setTopic] = useState("General Knowledge");
    const [input, setInput] = useState('');
    const [messages, setMessages] = useState([
        {
            from: 'ai',
            text: "Hey! No worries—we can turn this around. First, tell me: what topics are covered in the exam?",
        },
        {
            from: 'user',
            text: "Viva",
        },
    ]);

    useEffect(() => {
        const stored = JSON.parse(localStorage.getItem('userVivaPreference'));
        console.log("stored", stored);
        setTopic(stored.topic);
        setLanguage(stored.language);
    }, [topic, language]);

    // Webcam state
    const [webcamOn, setWebcamOn] = useState(false);
    const videoRef = useRef(null);
    const streamRef = useRef(null);
    const avatarVideoRef = useRef();
    const avatarAudioRef = useRef();

    const speakText = (text) => {
        return new Promise((resolve, reject) => {
            if (!avatarSynthesizer) return reject("Avatar not ready.");
            avatarSynthesizer.speakTextAsync(
                text,
                (result) => {
                    if (result.reason === SpeechSDK.ResultReason.SynthesizingAudioCompleted) resolve();
                    else reject("Synthesis failed.");
                },
                (error) => reject(error)
            );
        });
    };

    const handleOnTrack = (event) => {
        if (event.track.kind === "video") {
            avatarVideoRef.current.srcObject = event.streams[0];
        } else if (event.track.kind === "audio") {
            avatarAudioRef.current.srcObject = event.streams[0];
            avatarAudioRef.current.muted = false;
            avatarAudioRef.current.play().catch(() => { });
        }
    };

    const startSession = async () => {
        console.log("Starting session...");
        // const allowed = await requestMicAccess();
        // if (!allowed) return;
        // await startUserCamera();

        const pc = createWebRTCConnection(
            avatarAppConfig.iceUrl,
            avatarAppConfig.iceUsername,
            avatarAppConfig.iceCredential
        );
        pc.ontrack = handleOnTrack;
        pc.addTransceiver("video", { direction: "sendrecv" });
        pc.addTransceiver("audio", { direction: "sendrecv" });

        const avatar = createAvatarSynthesizer({
            voice: language === "hindi" ? "hi-IN-SwaraNeural" : "en-IN-NeerjaNeural",
        });
        await avatar.startAvatarAsync(pc);
        setAvatarSynthesizer(avatar);
        setInterviewStarted(true);
        // setStatus("Interview started.");
    };

    // Speech recognition state
    const {
        transcript,
        listening,
        resetTranscript,
        browserSupportsSpeechRecognition
    } = useSpeechRecognition();
    const [micActive, setMicActive] = useState(false);

    // Modal state
    const [showPreferenceModal, setShowPreferenceModal] = useState(true);
    const [userPreference, setUserPreference] = useState(null);
    const [initialPref, setInitialPref] = useState({ topic: '', language: '' });


    const askOpenAi = async (input) => {
        console.log("chat input input", input);
        setMessages([...messages, { from: 'user', text: input }]);
        try {
            const response = await axios.post(
                'https://api.openai.com/v1/chat/completions',
                {
                    model: 'gpt-3.5-turbo',
                    messages: [
                        {
                            role: "system",
                            content: `You are a professional interviewer. Ask one question at a time about "${topic}". The last answer is: ${messages[messages.length - 1]?.text || "none"}`
                        },
                        { role: "user", content: input },
                    ],
                    temperature: 0,
                },
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${import.meta.env.VITE_OPENAI_API_KEY}`
                    }
                }
            );

            console.log("response from ai", response);

            const reply = response.data.choices[0].message.content;
            console.log("reply", reply);
            setMessages([...messages, { from: 'ai', text: reply }]);
            await speakText(reply);
            return reply;
        } catch (err) {
            console.log("error in fetching", err.response?.data || err.message || err);
        }
    };

    useEffect(() => {
        console.log('messages--', messages);
    }, [messages]);


    // On mount, check localStorage for userVivaPreference
    useEffect(() => {
        const stored = localStorage.getItem('userVivaPreference');
        if (stored) {
            try {
                const parsed = JSON.parse(stored);
                setInitialPref({
                    topic: parsed.topic || '',
                    language: parsed.language || ''
                });
                setUserPreference(parsed);
            } catch {
                // ignore parse error
            }
        }
        setShowPreferenceModal(true); // Always show modal on load
    }, []);

    // Webcam logic
    useEffect(() => {
        if (webcamOn) {
            navigator.mediaDevices.getUserMedia({ video: true })
                .then(stream => {
                    if (videoRef.current) {
                        videoRef.current.srcObject = stream;
                        streamRef.current = stream;
                    }
                })
                .catch(() => {
                    setWebcamOn(false);
                });
        } else {
            if (streamRef.current) {
                streamRef.current.getTracks().forEach(track => track.stop());
                streamRef.current = null;
            }
            if (videoRef.current) {
                videoRef.current.srcObject = null;
            }
        }
        return () => {
            if (streamRef.current) {
                streamRef.current.getTracks().forEach(track => track.stop());
                streamRef.current = null;
            }
        };
    }, [webcamOn]);

    // Mic logic
    // useEffect(() => {
    //     if (micActive && browserSupportsSpeechRecognition) {
    //         resetTranscript();
    //         SpeechRecognition.startListening({ continuous: true, language: 'en-IN' });
    //     } else {
    //         SpeechRecognition.stopListening();
    //     }
    //     // When mic is stopped, update input with transcript
    //     if (!micActive && transcript) {
    //         setInput(transcript);
    //     }
    //     // eslint-disable-next-line
    // }, [micActive]);

    // Update input live while listening
    useEffect(() => {
        if (micActive && listening) {
            setInput(transcript);
        }
        // eslint-disable-next-line
    }, [transcript, listening]);

    // Send message
    const handleSend = async () => {
        if (!input.trim()) return;
        // setMessages([...messages, { from: 'user', text: input }]);
        askOpenAi(input);
        setInput('');
    };

    // Toggle webcam
    const handleToggleWebcam = () => {
        setWebcamOn(prev => !prev);
    };

    // Toggle mic
    const handleToggleMic = () => {
        if (!browserSupportsSpeechRecognition) {
            alert('Your browser does not support speech recognition.');
            return;
        }
        setMicActive(prev => !prev);
    };
    const recognizerRef = useRef(null);
    const autoSendTimeoutRef = useRef(null);

    /* ───── 3. Azure Speech Recognition logic ───────────────────────────── */
    const startAzureMic = () => {
        /* configure recognizer */
        const speechConfig = SpeechSDK.SpeechConfig.fromSubscription(
            SPEECH_KEY,
            SPEECH_REGION
        );
        speechConfig.speechRecognitionLanguage = "en-IN";

        const audioConfig = SpeechSDK.AudioConfig.fromDefaultMicrophoneInput();
        const recognizer = new SpeechSDK.SpeechRecognizer(speechConfig, audioConfig);

        recognizer.recognizing = (_, e) => {
            /* live stream while speaking */
            setInput(e.result.text);

            // Clear any existing timeout when user is actively speaking
            if (autoSendTimeoutRef.current) {
                clearTimeout(autoSendTimeoutRef.current);
                autoSendTimeoutRef.current = null;
            }
        };

        recognizer.recognized = (_, e) => {
            if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech) {
                const finalText = e.result.text.trim();
                setInput(finalText);

                // Set timeout to auto-send after 1 second of silence
                if (finalText) {
                    autoSendTimeoutRef.current = setTimeout(() => {
                        handleSend();
                        autoSendTimeoutRef.current = null;
                    }, 200); // 1 second delay
                }
            }
        };

        recognizer.canceled = (_, e) => {
            console.error("Recognition canceled:", e);
            recognizer.stopContinuousRecognitionAsync();
            setMicActive(false);

            // Clear timeout on cancel
            if (autoSendTimeoutRef.current) {
                clearTimeout(autoSendTimeoutRef.current);
                autoSendTimeoutRef.current = null;
            }
        };

        recognizer.sessionStopped = () => {
            recognizer.stopContinuousRecognitionAsync();
            setMicActive(false);

            // Clear timeout on session stop
            if (autoSendTimeoutRef.current) {
                clearTimeout(autoSendTimeoutRef.current);
                autoSendTimeoutRef.current = null;
            }
        };

        recognizer.startContinuousRecognitionAsync();
        recognizerRef.current = recognizer;
    };

    const stopAzureMic = () => {
        recognizerRef.current?.stopContinuousRecognitionAsync(() => {
            recognizerRef.current?.close();
            recognizerRef.current = null;
        });

        // Clear any pending auto-send timeout when stopping mic
        if (autoSendTimeoutRef.current) {
            clearTimeout(autoSendTimeoutRef.current);
            autoSendTimeoutRef.current = null;
        }
    };

    /* toggle mic */
    useEffect(() => {
        if (micActive) startAzureMic();
        else stopAzureMic();
        // cleanup on unmount
        return stopAzureMic;
    }, [micActive]);


    return (
        <>
            {showPreferenceModal && (
                <PreferenceModal
                    onClose={() => setShowPreferenceModal(false)}
                    onSubmit={(pref) => {
                        setUserPreference(pref);
                        setInitialPref(pref);
                        setShowPreferenceModal(false);
                        localStorage.setItem("userVivaPreference", JSON.stringify(pref));
                        startSession();
                    }}
                    initialTopic={initialPref.topic}
                    initialLanguage={initialPref.language}
                />
            )}
            {!showPreferenceModal && (
                <div className="min-h-screen bg-white flex flex-col items-center justify-center py-8 px-2 md:px-0">
                    {/* Header */}
                    <div className="w-full max-w-5xl flex justify-between items-center mb-8">
                        <div className="text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600 font-bold text-3xl md:text-4xl" style={{ WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>
                            AI Interview - LIVE
                        </div>
                        <div className="flex gap-4 text-[#3734FF]">
                            <MdOutlineDownloadForOffline className="w-7 h-7 rounded-full flex items-center justify-center text-lg" />
                            <HiRefresh className="w-7 h-7 rounded-fullflex items-center justify-center text-lg" />
                            <IoCloseCircleOutline className="w-7 h-7 rounded-full flex items-center justify-center text-lg" />
                        </div>
                    </div>
                    {/* Add a button to change preference */}
                    {/* <div className="mb-4 w-full max-w-5xl flex justify-end">
                        <button
                            className="px-4 py-2 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold shadow-md hover:from-blue-600 hover:to-purple-600"
                            onClick={() => setShowPreferenceModal(true)}
                        >
                            Change Preference
                        </button>
                    </div> */}

                    <div className="w-full max-w-7xl border-1 border-gray-200 rounded-2xl p-6 flex flex-col md:flex-row gap-8 bg-[#D9D9D91A]">
                        {/* AI Cam Section */}
                        <div className="flex-1 flex flex-col items-center">
                            <div className="relative w-[70%] h-[100%] rounded-2xl overflow-hidden border border-gray-200 shadow-lg bg-white flex items-center justify-center mb-0">
                                {/* AI Cam Image Placeholder */}
                                <div className="absolute top-4 right-4 z-10">
                                    {/* Placeholder for waveform icon */}
                                    <span className="w-10 h-6 bg-blue-200 rounded-full flex items-center justify-center text-blue-700 font-bold">〰️</span>
                                </div>
                                {/* <img src="https://www.indianext.co.in/wp-content/uploads/2022/03/portrait-female-teacher-holding-notepad-green.jpg" alt="AI Cam" className="object-cover w-full h-full" /> */}
                                <video
                                    ref={avatarVideoRef}
                                    className="w-full h-full object-cover bg-white"
                                    autoPlay
                                    muted
                                />
                                <audio ref={avatarAudioRef} />
                                {/* AI message overlay */}
                                <div className="absolute bottom-4 left-1/2 -translate-x-1/2 w-[90%] bg-gradient-to-r from-[#6C9DFF] to-[#755CFF] rounded-lg px-4 py-2 text-white text-sm shadow-lg">
                                    {messages.filter(m => m.from === 'ai').slice(-1)[0]?.text}
                                </div>
                            </div>
                        </div>

                        {/* User Cam & Chat Section */}
                        <div className="flex-1 flex flex-col items-center">
                            <div className="w-[90%] h-[400px] rounded-2xl overflow-hidden border border-gray-200 shadow-lg bg-gray-100 flex items-center justify-center mb-0">
                                {webcamOn ? (
                                    <video ref={videoRef} autoPlay muted className="object-cover w-full h-full" />
                                ) : (
                                    <div className="w-full h-full flex items-center justify-center text-gray-400 text-2xl">Webcam Off</div>
                                )}
                            </div>
                            {/* Mic and Camera buttons below video */}
                            <div className="flex gap-6 mt-4 mb-4 justify-center">
                                <button
                                    className={`w-12 h-12 rounded-full flex items-center justify-center text-white text-2xl shadow-md transition ${micActive ? 'bg-gradient-to-r from-[#A723FF] to-[#1A39FF]' : 'bg-red-600'}`}
                                    onClick={handleToggleMic}
                                >
                                    <FaMicrophone className="w-6 h-6" />
                                </button>
                                <button
                                    className={`w-12 h-12 rounded-full flex items-center justify-center text-white text-2xl shadow-md transition ${webcamOn ? 'bg-gradient-to-r from-[#A723FF] to-[#1A39FF]' : 'bg-red-600'}`}
                                    onClick={handleToggleWebcam}
                                >
                                    <IoIosVideocam className="w-6 h-6" />
                                </button>
                            </div>
                            {/* Chat input below buttons */}
                            <div className="w-full flex items-center gap-2 mt-2">
                                <input
                                    className="flex-1 px-4 py-3 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-200 focus:border-transparent text-base"
                                    type="text"
                                    placeholder="Your Text Here....."
                                    value={input}
                                    onChange={e => setInput(e.target.value)}
                                    onKeyDown={e => { if (e.key === 'Enter') handleSend(); }}
                                    disabled={micActive}
                                />
                                <button
                                    className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-200 to-blue-200 flex items-center justify-center text-gray-700 hover:text-purple-700 transition text-xl"
                                    onClick={handleSend}
                                    disabled={micActive}
                                >
                                    <IoSend className="w-6 h-6" />
                                </button>
                            </div>

                        </div>

                    </div>
                    {/* <button
                        className="px-4 py-2 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold shadow-md hover:from-blue-600 hover:to-purple-600"
                        onClick={startSession}>Start Interview</button> */}
                </div>
            )}
        </>
    );
};

export default Viva;
